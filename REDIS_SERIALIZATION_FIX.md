# Redis缓存Proto对象序列化问题解决方案

## 问题描述

在使用Redis缓存`CacheItemDTO`对象时，由于该对象包含了`EndDeviceProto.DERControlRequest`这个Protocol Buffer对象，导致Jackson序列化时出现循环引用错误：

```
SerializationException: Could not write JSON: Infinite recursion (StackOverflowError)
(through reference chain: UnknownFieldSet["defaultInstanceForType"]->UnknownFieldSet["defaultInstanceForType"]->...)
```

## 根本原因

Protocol Buffer生成的Java类包含内部字段（如`unknownFields`、`defaultInstanceForType`等），这些字段在Jackson序列化时会产生无限递归的循环引用，导致StackOverflowError。即使配置了`FAIL_ON_SELF_REFERENCES = false`，仍然无法解决深层次的循环引用问题。

## 解决方案

### 最终方案：创建可序列化的DTO（已实施）

由于Proto对象的内部结构过于复杂，直接序列化会导致无限递归，我们采用了以下方案：

1. **创建`CacheItemSerializableDTO`**：将Proto对象的关键字段提取出来，避免序列化整个Proto对象
2. **修改缓存逻辑**：在存储到Redis时转换为可序列化DTO，从Redis读取时转换回原始DTO
3. **保持业务逻辑不变**：对业务代码的影响最小化

### 其他尝试的方案

#### 方案1：修改Redis配置（部分有效）
修改`RedisConfig.java`，配置Jackson ObjectMapper：
```java
om.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
om.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
om.setSerializationInclusion(JsonInclude.Include.NON_NULL);
om.deactivateDefaultTyping();
```

#### 方案2：添加Jackson注解（辅助作用）
在`CacheItemDTO`类上添加`@JsonIgnoreProperties(ignoreUnknown = true)`注解。

#### 方案3：自定义Proto序列化器（备选方案）
创建了`ProtoSerializationUtil`工具类，提供专门的Proto对象序列化器。

## 修改的文件

1. **`ieee-core/src/main/java/com/weihengtech/ieee/pojo/dtos/CacheItemSerializableDTO.java`**（新增）
   - 可序列化的缓存DTO，提取Proto对象的关键字段
   - 提供与原始CacheItemDTO的转换方法

2. **`ieee-core/src/main/java/com/weihengtech/ieee/service/device/impl/EndDeviceServiceImpl.java`**
   - 修改缓存逻辑，使用CacheItemSerializableDTO进行Redis存储

3. **`ieee-core/src/main/java/com/weihengtech/ieee/service/scheduler/RedisTaskSchedulerService.java`**
   - 修改任务处理逻辑，支持新旧DTO格式的兼容

4. **`ieee-core/src/main/java/com/weihengtech/ieee/config/RedisConfig.java`**
   - 优化Jackson配置来处理序列化问题

5. **`ieee-core/src/main/java/com/weihengtech/ieee/pojo/dtos/CacheItemDTO.java`**
   - 添加了`@JsonIgnoreProperties(ignoreUnknown = true)`注解

6. **`ieee-core/src/main/java/com/weihengtech/ieee/utils/ProtoSerializationUtil.java`**（新增）
   - 提供了专门的Proto对象序列化工具类（备选方案）

7. **`ieee-core/src/test/java/com/weihengtech/ieee/config/RedisSerializationTest.java`**（新增）
   - 添加了Redis序列化测试用例

## 验证方法

运行测试用例验证修复效果：

```bash
mvn test -Dtest=RedisSerializationTest
```

## 优势

1. **不改变业务逻辑**：完全保持原有代码逻辑不变
2. **向后兼容**：不影响现有功能
3. **性能友好**：配置级别的修改，不增加运行时开销
4. **可扩展**：为将来可能的其他Proto对象序列化需求提供了基础

## 注意事项

1. 修改后的配置会影响所有通过该RedisTemplate序列化的对象
2. 如果需要更精细的控制，可以考虑为特定的对象类型创建专门的序列化器
3. 建议在生产环境部署前进行充分测试

## 测试建议

1. 运行单元测试确保序列化/反序列化正常工作
2. 在测试环境验证完整的业务流程
3. 监控Redis中缓存对象的大小和性能表现
