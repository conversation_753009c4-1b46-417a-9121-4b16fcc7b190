package com.weihengtech.ieee.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * redis config
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27 16:42
 */
@Configuration
public class RedisConfig {

    @Bean
    @SuppressWarnings(value = "all")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(factory);
        // json序列化
        Jackson2JsonRedisSerializer<Object> jsonSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);

        // 配置Jackson处理循环引用和Proto对象序列化问题
        om.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
        om.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 设置序列化包含策略，只序列化非空字段
        om.setSerializationInclusion(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL);

        // 禁用默认类型信息以避免Proto对象的复杂类型处理
        om.deactivateDefaultTyping();

        jsonSerializer.setObjectMapper(om);
        // string 序列化
        StringRedisSerializer stringSerializer = new StringRedisSerializer();

        // key采用的string序列化
        redisTemplate.setKeySerializer(stringSerializer);
        // hash 的key采用string序列化
        redisTemplate.setHashKeySerializer(stringSerializer);
        // value采用json序列化
        redisTemplate.setValueSerializer(jsonSerializer);
        // hash 的value采用json序列化
        redisTemplate.setHashValueSerializer(jsonSerializer);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }
}
