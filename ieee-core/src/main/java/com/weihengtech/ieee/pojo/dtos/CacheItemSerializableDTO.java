package com.weihengtech.ieee.pojo.dtos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.pojo.dos.DeviceListDO;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 可序列化的任务缓存对象
 * 避免直接序列化Proto对象，而是将Proto对象转换为可序列化的字段
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class CacheItemSerializableDTO {

    private Long taskStartTime;

    private StorageEnergyInfoDO storageInfo;

    private DeviceListDO deviceInfo;

    // Proto对象的关键字段，避免序列化整个Proto对象
    private String lfdi;
    private Long id;
    private String mrid;
    private String type;
    private Long startTime;
    private Long duration;
    private Long endTime;
    
    // DERControlBase的关键字段
    private Boolean opModEnergize;
    private Boolean opModConnect;
    private Integer opModMaxLimW;
    private Long opModExpLimW;
    private Long opModImpLimW;
    private Long opModGenLimW;
    private Long opModLoadLimW;
    private Integer opModTargetW;
    private Integer opModFixedW;
    private Long opModStorageTargetW;

    /**
     * 从CacheItemDTO转换为可序列化的DTO
     */
    public static CacheItemSerializableDTO fromCacheItemDTO(CacheItemDTO cacheItemDTO) {
        if (cacheItemDTO == null) {
            return null;
        }

        CacheItemSerializableDTOBuilder builder = CacheItemSerializableDTO.builder()
                .taskStartTime(cacheItemDTO.getTaskStartTime())
                .storageInfo(cacheItemDTO.getStorageInfo())
                .deviceInfo(cacheItemDTO.getDeviceInfo());

        // 提取Proto对象的字段
        EndDeviceProto.DERControlRequest request = cacheItemDTO.getRequest();
        if (request != null) {
            builder.lfdi(request.getLfdi())
                    .id(request.getId())
                    .mrid(request.getMrid())
                    .type(request.getType())
                    .startTime(request.getStartTime())
                    .duration(request.getDuration())
                    .endTime(request.getEndTime());

            // 提取控制参数
            EndDeviceProto.DERControlBase control = request.getControl();
            if (control != null) {
                builder.opModEnergize(control.hasOpModEnergize() ? control.getOpModEnergize() : null)
                        .opModConnect(control.hasOpModConnect() ? control.getOpModConnect() : null)
                        .opModMaxLimW(control.hasOpModMaxLimW() ? control.getOpModMaxLimW() : null)
                        .opModExpLimW(control.hasOpModExpLimW() ? control.getOpModExpLimW() : null)
                        .opModImpLimW(control.hasOpModImpLimW() ? control.getOpModImpLimW() : null)
                        .opModGenLimW(control.hasOpModGenLimW() ? control.getOpModGenLimW() : null)
                        .opModLoadLimW(control.hasOpModLoadLimW() ? control.getOpModLoadLimW() : null)
                        .opModTargetW(control.hasOpModTargetW() ? control.getOpModTargetW() : null)
                        .opModFixedW(control.hasOpModFixedW() ? control.getOpModFixedW() : null)
                        .opModStorageTargetW(control.hasOpModStorageTargetW() ? control.getOpModStorageTargetW() : null);
            }
        }

        return builder.build();
    }

    /**
     * 转换回CacheItemDTO
     */
    public CacheItemDTO toCacheItemDTO() {
        // 重建Proto对象
        EndDeviceProto.DERControlBase.Builder controlBuilder = EndDeviceProto.DERControlBase.newBuilder();
        
        if (opModEnergize != null) controlBuilder.setOpModEnergize(opModEnergize);
        if (opModConnect != null) controlBuilder.setOpModConnect(opModConnect);
        if (opModMaxLimW != null) controlBuilder.setOpModMaxLimW(opModMaxLimW);
        if (opModExpLimW != null) controlBuilder.setOpModExpLimW(opModExpLimW);
        if (opModImpLimW != null) controlBuilder.setOpModImpLimW(opModImpLimW);
        if (opModGenLimW != null) controlBuilder.setOpModGenLimW(opModGenLimW);
        if (opModLoadLimW != null) controlBuilder.setOpModLoadLimW(opModLoadLimW);
        if (opModTargetW != null) controlBuilder.setOpModTargetW(opModTargetW);
        if (opModFixedW != null) controlBuilder.setOpModFixedW(opModFixedW);
        if (opModStorageTargetW != null) controlBuilder.setOpModStorageTargetW(opModStorageTargetW);

        EndDeviceProto.DERControlRequest.Builder requestBuilder = EndDeviceProto.DERControlRequest.newBuilder()
                .setControl(controlBuilder.build());
        
        if (lfdi != null) requestBuilder.setLfdi(lfdi);
        if (id != null) requestBuilder.setId(id);
        if (mrid != null) requestBuilder.setMrid(mrid);
        if (type != null) requestBuilder.setType(type);
        if (startTime != null) requestBuilder.setStartTime(startTime);
        if (duration != null) requestBuilder.setDuration(duration);
        if (endTime != null) requestBuilder.setEndTime(endTime);

        return CacheItemDTO.builder()
                .taskStartTime(taskStartTime)
                .storageInfo(storageInfo)
                .deviceInfo(deviceInfo)
                .request(requestBuilder.build())
                .build();
    }
}
