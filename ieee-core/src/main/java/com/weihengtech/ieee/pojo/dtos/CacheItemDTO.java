package com.weihengtech.ieee.pojo.dtos;

import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.pojo.dos.DeviceListDO;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务缓存对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/23 19:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CacheItemDTO {

    private Long taskStartTime;

    private StorageEnergyInfoDO storageInfo;

    private DeviceListDO deviceInfo;

    private EndDeviceProto.DERControlRequest request;
}