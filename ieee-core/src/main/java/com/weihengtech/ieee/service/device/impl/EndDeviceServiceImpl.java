package com.weihengtech.ieee.service.device.impl;

import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.constants.Constants;
import com.weihengtech.ieee.delay.MqService;
import com.weihengtech.ieee.enums.ControlTypeEnum;
import com.weihengtech.ieee.enums.GridCompanyEnum;
import com.weihengtech.ieee.enums.SeriesEnum;
import com.weihengtech.ieee.interfaces.DoActionApi;
import com.weihengtech.ieee.pojo.dos.DeviceListDO;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import com.weihengtech.ieee.pojo.dtos.CacheItemDTO;
import com.weihengtech.ieee.pojo.dtos.CacheItemSerializableDTO;
import com.weihengtech.ieee.pojo.vos.WriteBatchDeviceVO;
import com.weihengtech.ieee.pojo.vos.WriteDeviceVO;
import com.weihengtech.ieee.service.device.DeviceListService;
import com.weihengtech.ieee.service.device.EndDeviceService;
import com.weihengtech.ieee.service.device.StorageEnergyInfoService;
import com.weihengtech.ieee.service.modbus.NaWriteMapUtil;
import com.weihengtech.ieee.service.passthrough.SpecificServService;
import com.weihengtech.ieee.service.passthrough.StrategyService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.retry.RetryException;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 设备控制服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/12 15:26
 */
@Service
@Slf4j
public class EndDeviceServiceImpl implements EndDeviceService, DoActionApi {

    @Value("${task.call.period:#{10}}")
    private long period;
    @Value("${task.call.extra:#{25}}")
    private long extra;
    @Value("${current.grid.company:3}")
    private int currentGridCompany;

    @Resource
    private StorageEnergyInfoService storageEnergyInfoService;
    @Resource
    private StrategyService strategyService;
    @Resource
    private DeviceListService deviceListService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private MqService mqService;

    @Override
    @Retryable(retryFor = {RetryException.class})
    public void setDefaultControl(EndDeviceProto.DefaultDERControl request) {
        // 获取设备设计指标相关信息
        StorageEnergyInfoDO storageInfo = storageEnergyInfoService.getByLfdi(request.getLfdi());
        // 获取设备详情
        DeviceListDO deviceInfo = deviceListService.getById(storageInfo.getDeviceId());
        if (deviceInfo.getState() < 0) {
            log.error("device: {} is offline", storageInfo.getDeviceId());
            return;
        }
        // 构建默认任务点位值
        List<WriteDeviceVO> values = buildDefaultVal(request, storageInfo);
        // 批量下发点位值
        SpecificServService specificServService = strategyService.chooseSpecificServ(deviceInfo);
        Boolean res;
        try {
            if (SeriesEnum.NA_Device.getId().equals(storageInfo.getDeviceType())) {
                res = specificServService.sendBatchWriteCommand(WriteBatchDeviceVO.builder()
                        .deviceId(deviceInfo.getWifiSn())
                        .valList(values)
                        .build());
            } else {
                WriteDeviceVO param = values.get(0);
                param.setDeviceId(deviceInfo.getWifiSn());
                res = specificServService.sendWriteCommand(param);
            }
        } catch (Exception e) {
            log.error(String.format("%s send write command error: ", deviceInfo.getDeviceSn()), e);
            throw new RetryException(String.format("%s send write command error: ", deviceInfo.getDeviceSn()));
        }
        if (!res) {
            log.error(String.format("%s send write command error: ", deviceInfo.getDeviceSn()));
            throw new RetryException(String.format("%s send write command error: ", deviceInfo.getDeviceSn()));
        }
    }

    @Override
    public void setControl(EndDeviceProto.DERControlRequest request) {
        StorageEnergyInfoDO storageInfo = storageEnergyInfoService.getByLfdi(request.getLfdi());
        DeviceListDO deviceInfo = deviceListService.getById(storageInfo.getDeviceId());
        if (deviceInfo.getState() < 0) {
            log.error("device: {} is offline", storageInfo.getDeviceId());
//            return;
        }
        String type = request.getType();
        String taskName = buildTaskName(deviceInfo, storageInfo);
        if (ControlTypeEnum.isDone(type)) {
            redisTemplate.delete(buildTaskKey(taskName));
            disableControl(deviceInfo);
        } else {
            // 5s内重复触发不执行
            if (redisTemplate.hasKey(buildTaskKey(taskName))) {
                Long taskStartTime = (Long) redisTemplate.opsForValue().get(buildTaskKey(taskName));
                if (System.currentTimeMillis() - taskStartTime.longValue() < 5000) {
                    log.info("{} has already began", taskName);
                    return;
                }
            }
            CacheItemDTO cacheItem = CacheItemDTO.builder()
                    .taskStartTime(System.currentTimeMillis())
                    .storageInfo(storageInfo)
                    .deviceInfo(deviceInfo)
                    .request(request)
                    .build();

            // 转换为可序列化的DTO来避免Proto对象序列化问题
            CacheItemSerializableDTO serializableItem = CacheItemSerializableDTO.fromCacheItemDTO(cacheItem);
            redisTemplate.opsForValue().set(buildTaskKey(taskName), serializableItem, request.getDuration(), TimeUnit.SECONDS);
//            mqService.startTask(Task.builder()
//                    .name(taskName)
//                    .service(this)
//                    .param(new Object[]{request, storageInfo, deviceInfo})
//                    .period(period)
//                    .build());
        }

    }

    @Override
    public void doAction(Object... param) {
        EndDeviceProto.DERControlRequest request = (EndDeviceProto.DERControlRequest) param[0];
        StorageEnergyInfoDO storageInfo = (StorageEnergyInfoDO) param[1];
        DeviceListDO deviceInfo = (DeviceListDO) param[2];
        log.info("{}的setControl任务开始----------------", deviceInfo.getDeviceSn());
        Boolean serverOnline = (Boolean) redisTemplate.opsForValue().get(Constants.UTILITY_SERVER_STATUS);
        if (Boolean.FALSE.equals(serverOnline)) {
            log.info("{}的setControl任务结束: 服务离线，无需下发----------------", deviceInfo.getDeviceSn());
            return;
        }
        // 构建控制任务点位值
        List<WriteDeviceVO> values = buildEnableVal(request, storageInfo);
        // 批量下发点位值
        SpecificServService specificServService = strategyService.chooseSpecificServ(deviceInfo);
        Boolean res;
        try {
            if (SeriesEnum.NA_Device.getId().equals(storageInfo.getDeviceType())) {
                res = specificServService.sendBatchWriteCommand(WriteBatchDeviceVO.builder()
                        .deviceId(deviceInfo.getWifiSn())
                        .valList(values)
                        .build());
            } else {
                WriteDeviceVO writeParam = values.get(0);
                writeParam.setDeviceId(deviceInfo.getWifiSn());
                res = specificServService.sendWriteCommandAsync(writeParam);
            }
        } catch (Exception e) {
            log.error(String.format("%s send write command error: ", deviceInfo.getDeviceSn()), e);
            throw new RetryException(String.format("%s send write command error: ", deviceInfo.getDeviceSn()));
        }
        if (!res) {
            log.error(String.format("%s send write command error: ", deviceInfo.getDeviceSn()));
            throw new RetryException(String.format("%s send write command error: ", deviceInfo.getDeviceSn()));
        }
        log.info("{}的setControl任务结束----------------", deviceInfo.getDeviceSn());
    }

    /** 关闭控制任务 */
    private void disableControl(DeviceListDO deviceInfo) {
        SpecificServService specificServService = strategyService.chooseSpecificServ(deviceInfo);
        specificServService.sendWriteCommand(WriteDeviceVO.builder()
                .deviceId(deviceInfo.getWifiSn())
                .slaveId(1)
                .startAddress(61001)
                .len(1)
                .values(Collections.singletonList(0))
                .build());
    }

    /** 构建默认任务指令点位值 */
    private List<WriteDeviceVO> buildDefaultVal(EndDeviceProto.DefaultDERControl request, StorageEnergyInfoDO storageInfo) {
        List<WriteDeviceVO> resList = new ArrayList<>();
        // 南澳部分
        List<Integer> values = new ArrayList<>(8);
        EndDeviceProto.DERControlBase control = request.getControl();
        values.add(1);
        values.add(!control.hasOpModEnergize() ? 1 : control.getOpModEnergize() ? 1 : 0);
        values.add(request.getSetGradW());
        // 点位单位是10W，下发数据是W，需要转换单位
        values.add(calDevGenLimit(control, storageInfo));
        values.add(control.hasOpModExpLimW() ? (int) (control.getOpModExpLimW() * 0.1) : storageInfo.getDesignMaxPower());
        if (GridCompanyEnum.Synergy.getId() == storageInfo.getGridCompany()) {
            // 61106保留点位，无用
            values.add(0);
            values.add(control.hasOpModStorageTargetW() ? 1 : 0);
            if (control.hasOpModStorageTargetW()) {
                values.add((int) (control.getOpModStorageTargetW() * 0.1));
            }
            resList.add(WriteDeviceVO.builder()
                    .slaveId(1)
                    .len(control.hasOpModStorageTargetW() ? 8 : 7)
                    .startAddress(61101)
                    .values(values)
                    .build());
        } else {
            resList.add(WriteDeviceVO.builder()
                    .slaveId(1)
                    .len(5)
                    .startAddress(61101)
                    .values(values)
                    .build());
        }

        // 北美机部分
        if (SeriesEnum.NA_Device.getId().equals(storageInfo.getDeviceType())) {
            resList.add(NaWriteMapUtil.buildOpModTrip(control, true));
            resList.add(NaWriteMapUtil.buildOpModVoltVar(request.getControl().getOpModVoltVar(), true));
            resList.addAll(NaWriteMapUtil.buildOpModVoltWatt(request.getControl().getOpModVoltWatt(), true));
            resList.addAll(NaWriteMapUtil.buildOpModFreqWatt(request.getControl().getOpModFreqWatt(), true));
            EndDeviceProto.PowerFactor factor = NaWriteMapUtil.findExistsOne(control.getOpModFixedPFInjectW(), control.getOpModFixedPFAbsorbW());
            Optional.ofNullable(factor).map(i -> WriteDeviceVO.builder().slaveId(1)
                    .startAddress(61801).len(1).values(Collections.singletonList((int) factor.getValue())).build()).ifPresent(resList :: add);
            resList.add(WriteDeviceVO.builder().slaveId(1).startAddress(61744).len(1).values(Collections.singletonList(control.getOpModTargetW() * 100)).build());
        }
        return resList;
    }

    /** 构建控制任务指令点位值 */
    private List<WriteDeviceVO> buildEnableVal(EndDeviceProto.DERControlRequest request, StorageEnergyInfoDO storageInfo) {
        List<WriteDeviceVO> resList = new ArrayList<>();
        // 南澳部分
        List<Integer> values = new ArrayList<>(8);
        EndDeviceProto.DERControlBase control = request.getControl();
        values.add(1);
        values.add(!control.hasOpModEnergize() ? 1 : control.getOpModEnergize() ? 1 : 0);
        values.add(0);
        values.add(calDevGenLimit(control, storageInfo));
        values.add(control.hasOpModExpLimW() ? (int) (control.getOpModExpLimW() * 0.1) : storageInfo.getDesignMaxPower());

        // 给读写、网络耗时等留出余地
        values.add((int) (period + extra));
        if (GridCompanyEnum.Synergy.getId() == storageInfo.getGridCompany()) {
            values.add(control.hasOpModStorageTargetW() ? 1 : 0);
            if (control.hasOpModStorageTargetW()) {
                values.add((int) (control.getOpModStorageTargetW() * 0.1));
            }
            resList.add(WriteDeviceVO.builder()
                    .slaveId(1)
                    .startAddress(61001)
                    .len(control.hasOpModStorageTargetW() ? 8 : 7)
                    .values(values)
                    .build());
        } else {
            resList.add(WriteDeviceVO.builder()
                    .slaveId(1)
                    .startAddress(61001)
                    .len(6)
                    .values(values)
                    .build());
        }

        // 北美机部分
        if (SeriesEnum.NA_Device.getId().equals(storageInfo.getDeviceType())) {
            resList.add(NaWriteMapUtil.buildOpModTrip(control, false));
            resList.add(NaWriteMapUtil.buildOpModVoltVar(request.getControl().getOpModVoltVar(), false));
            resList.addAll(NaWriteMapUtil.buildOpModVoltWatt(request.getControl().getOpModVoltWatt(), false));
            resList.addAll(NaWriteMapUtil.buildOpModFreqWatt(request.getControl().getOpModFreqWatt(), false));
            EndDeviceProto.PowerFactor factor = NaWriteMapUtil.findExistsOne(control.getOpModFixedPFInjectW(), control.getOpModFixedPFAbsorbW());
            Optional.ofNullable(factor).map(i -> WriteDeviceVO.builder().slaveId(1)
                    .startAddress(61501).len(1).values(Collections.singletonList((int) factor.getValue())).build()).ifPresent(resList :: add);
            resList.add(WriteDeviceVO.builder().slaveId(1).startAddress(61444).len(1).values(Collections.singletonList(control.getOpModTargetW() * 100)).build());
        }
        return resList;
    }


    /**
     * 1.如果OpModMaxLimW，opModGenLimW都为空则取额定，如果1个不为空取1个，2个不为空取最小的
     * 2.opModExpLimW就取实际值，如果为空发0
     *
     * @param control  入参
     * @param storageInfo 动态输出参数
     * @return 设备侧功率值
     */
    private Integer calDevGenLimit(EndDeviceProto.DERControlBase control, StorageEnergyInfoDO storageInfo) {
        if (!control.hasOpModGenLimW() && !control.hasOpModMaxLimW()) {
            return storageInfo.getDesignMaxPower();
        } else if (control.hasOpModGenLimW() && !control.hasOpModMaxLimW()) {
            return (int) (control.getOpModGenLimW() * 0.1);
        } else if (!control.hasOpModGenLimW()) {
            return (int) (storageInfo.getDesignMaxPower() * (control.getOpModMaxLimW() * 0.0001));
        } else {
            return (int)(Math.min(control.getOpModGenLimW() * 0.1, storageInfo.getDesignMaxPower() * (control.getOpModMaxLimW() * 0.0001)));
        }
    }

    /** 构建任务名称 */
    private String buildTaskName(DeviceListDO deviceInfo, StorageEnergyInfoDO storageEnergyInfo) {
        return String.format("%s_%s_%s", deviceInfo.getId(), deviceInfo.getDeviceSn(), storageEnergyInfo.getLfdi());
    }

    private String buildTaskKey(String taskName) {
        return String.format(Constants.TASK_KEY, currentGridCompany, taskName);
    }
}
