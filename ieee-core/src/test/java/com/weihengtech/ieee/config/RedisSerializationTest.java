package com.weihengtech.ieee.config;

import com.weihengtech.ieee.EndDeviceProto;
import com.weihengtech.ieee.pojo.dos.DeviceListDO;
import com.weihengtech.ieee.pojo.dos.StorageEnergyInfoDO;
import com.weihengtech.ieee.pojo.dtos.CacheItemDTO;
import com.weihengtech.ieee.pojo.dtos.CacheItemSerializableDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis序列化测试
 * 测试新的CacheItemSerializableDTO的序列化和反序列化
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/24
 */
@SpringBootTest
@ActiveProfiles("test")
public class RedisSerializationTest {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Test
    public void testCacheItemSerializableDTOSerialization() {
        // 创建测试数据
        StorageEnergyInfoDO storageInfo = new StorageEnergyInfoDO();
        storageInfo.setDeviceId(1L);
        storageInfo.setLfdi("test-lfdi");
        storageInfo.setDesignMaxPower(5000);

        DeviceListDO deviceInfo = new DeviceListDO();
        deviceInfo.setId(1L);
        deviceInfo.setDeviceSn("test-device-sn");
        deviceInfo.setWifiSn("test-wifi-sn");
        deviceInfo.setState(1);

        // 创建Proto对象
        EndDeviceProto.DERControlRequest request = EndDeviceProto.DERControlRequest.newBuilder()
                .setLfdi("test-lfdi")
                .setId(1L)
                .setMrid("test-mrid")
                .setType("start")
                .setStartTime(System.currentTimeMillis() / 1000)
                .setDuration(3600)
                .setControl(EndDeviceProto.DERControlBase.newBuilder()
                        .setOpModEnergize(true)
                        .setOpModConnect(true)
                        .setOpModMaxLimW(100)
                        .setOpModExpLimW(1000L)
                        .build())
                .build();

        // 创建原始CacheItemDTO
        CacheItemDTO originalCacheItem = CacheItemDTO.builder()
                .taskStartTime(System.currentTimeMillis())
                .storageInfo(storageInfo)
                .deviceInfo(deviceInfo)
                .request(request)
                .build();

        // 转换为可序列化的DTO
        CacheItemSerializableDTO serializableItem = CacheItemSerializableDTO.fromCacheItemDTO(originalCacheItem);

        String testKey = "test:cache:serializable:item";

        try {
            // 测试序列化到Redis
            redisTemplate.opsForValue().set(testKey, serializableItem, 60, TimeUnit.SECONDS);
            
            // 测试从Redis反序列化
            Object retrieved = redisTemplate.opsForValue().get(testKey);
            
            assertNotNull(retrieved, "从Redis获取的对象不应为null");
            assertTrue(retrieved instanceof CacheItemSerializableDTO, "获取的对象应该是CacheItemSerializableDTO类型");
            
            CacheItemSerializableDTO retrievedItem = (CacheItemSerializableDTO) retrieved;
            
            // 验证基本字段
            assertEquals(serializableItem.getTaskStartTime(), retrievedItem.getTaskStartTime());
            assertNotNull(retrievedItem.getStorageInfo());
            assertNotNull(retrievedItem.getDeviceInfo());
            
            // 验证Proto字段
            assertEquals("test-lfdi", retrievedItem.getLfdi());
            assertEquals(Long.valueOf(1L), retrievedItem.getId());
            assertEquals("test-mrid", retrievedItem.getMrid());
            assertEquals("start", retrievedItem.getType());
            assertEquals(Boolean.TRUE, retrievedItem.getOpModEnergize());
            assertEquals(Boolean.TRUE, retrievedItem.getOpModConnect());
            assertEquals(Integer.valueOf(100), retrievedItem.getOpModMaxLimW());
            assertEquals(Long.valueOf(1000L), retrievedItem.getOpModExpLimW());
            
            // 测试转换回原始DTO
            CacheItemDTO convertedBack = retrievedItem.toCacheItemDTO();
            assertNotNull(convertedBack);
            assertNotNull(convertedBack.getRequest());
            assertEquals("test-lfdi", convertedBack.getRequest().getLfdi());
            assertEquals(1L, convertedBack.getRequest().getId());
            assertEquals("test-mrid", convertedBack.getRequest().getMrid());
            assertEquals("start", convertedBack.getRequest().getType());
            
            System.out.println("Redis可序列化DTO测试成功！");
            
        } catch (Exception e) {
            fail("Redis可序列化DTO测试失败: " + e.getMessage());
        } finally {
            // 清理测试数据
            redisTemplate.delete(testKey);
        }
    }
}
